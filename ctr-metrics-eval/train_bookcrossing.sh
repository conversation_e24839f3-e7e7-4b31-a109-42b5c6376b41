#!/bin/bash

# Set paths
DATA_PATH="/data/datasets/processed_datasets/bookcrossing"  # Path to BookCrossing data
OUTPUT_PATH="output_bookcrossing/"  # Path to save model outputs
SEED=2023

# Create output directory
mkdir -p $OUTPUT_PATH

# Set dataset name
DATASET="BookCrossing"

# # Train LR model
# echo "Training LR model on BookCrossing dataset..."
# python scripts/train.py \
#     --dataset_name $DATASET \
#     --dataset_path $DATA_PATH \
#     --model_name LR \
#     --model_output_path $OUTPUT_PATH \
#     --epochs 1 \
#     --batch_size 256 \
#     --embedding_dim 16 \
#     --learning_rate 0.001 \
#     --loss binary_crossentropy \
#     --optimizer adam \
#     --monitor_mode max \
#     --patience 2 \
#     --regularizer 0.00012 \
#     --task binary_classification \
#     --every_x_epochs 1 \
#     --seed $SEED

# # Train FM model
# echo "Training FM model on BookCrossing dataset..."
# python scripts/train.py \
#     --dataset_name $DATASET \
#     --dataset_path $DATA_PATH \
#     --model_name FM \
#     --model_output_path $OUTPUT_PATH \
#     --epochs 3 \
#     --batch_size 256 \
#     --embedding_dim 32 \
#     --learning_rate 0.001 \
#     --loss binary_crossentropy \
#     --optimizer adam \
#     --monitor_mode max \
#     --patience 2 \
#     --regularizer 0.0005 \
#     --task binary_classification \
#     --every_x_epochs 1 \
#     --seed $SEED

# # Train DeepFM model
# echo "Training DeepFM model on BookCrossing dataset..."
# python scripts/train.py \
#     --dataset_name $DATASET \
#     --dataset_path $DATA_PATH \
#     --model_name DeepFM \
#     --model_output_path $OUTPUT_PATH \
#     --epochs 4 \
#     --batch_size 512 \
#     --embedding_dim 10 \
#     --learning_rate 0.005\
#     --loss binary_crossentropy \
#     --optimizer adam \
#     --monitor_mode max \
#     --patience 2 \
#     --task binary_classification \
#     --regularizer 0.01 \
#     --every_x_epochs 1 \
#     --seed $SEED

# # Train AutoInt model
# echo "Training AutoInt model on BookCrossing dataset..."
# python scripts/train.py \
#     --dataset_name $DATASET \
#     --dataset_path $DATA_PATH \
#     --model_name AutoInt \
#     --model_output_path $OUTPUT_PATH \
#     --epochs 4 \
#     --batch_size 512 \
#     --embedding_dim 16 \
#     --learning_rate 0.001\
#     --loss binary_crossentropy \
#     --optimizer adam \
#     --monitor_mode max \
#     --patience 2 \
#     --task binary_classification \
#     --regularizer 0.01 \
#     --every_x_epochs 1 \
#     --seed $SEED

# # Train PNN model
# echo "Training PNN model on BookCrossing dataset..."
# python scripts/train.py \
#     --dataset_name $DATASET \
#     --dataset_path $DATA_PATH \
#     --model_name PNN \
#     --model_output_path $OUTPUT_PATH \
#     --epochs 3 \
#     --batch_size 512 \
#     --embedding_dim 10 \
#     --learning_rate 0.001\
#     --loss binary_crossentropy \
#     --optimizer adam \
#     --monitor_mode max \
#     --patience 2 \
#     --task binary_classification \
#     --regularizer 0.01 \
#     --every_x_epochs 1 \
#     --seed $SEED

# # Train FiGNN model
# echo "Training FiGNN model on BookCrossing dataset..."
# python scripts/train.py \
#     --dataset_name $DATASET \
#     --dataset_path $DATA_PATH \
#     --model_name FiGNN \
#     --model_output_path $OUTPUT_PATH \
#     --epochs 2 \
#     --batch_size 1024 \
#     --embedding_dim 16 \
#     --learning_rate 0.01\
#     --loss binary_crossentropy \
#     --optimizer adam \
#     --monitor_mode max \
#     --patience 2 \
#     --task binary_classification \
#     --regularizer 0.1 \
#     --every_x_epochs 1 \
#     --seed $SEED

# Train GraphFM model
echo "Training GraphFM model on BookCrossing dataset..."
python scripts/train.py \
    --dataset_name $DATASET \
    --dataset_path $DATA_PATH \
    --model_name GraphFM \
    --model_output_path $OUTPUT_PATH \
    --epochs 10 \
    --batch_size 1024 \
    --embedding_dim 16 \
    --learning_rate 0.01 \
    --loss binary_crossentropy \
    --optimizer adam \
    --monitor_mode max \
    --patience 3 \
    --task binary_classification \
    --regularizer 0.1 \
    --every_x_epochs 1 \
    --seed $SEED
