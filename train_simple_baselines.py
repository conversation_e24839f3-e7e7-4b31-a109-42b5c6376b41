#!/usr/bin/env python3
"""
Simple direct training of P5, AdaGIN, UniSRec on Amazon dataset
"""

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
from tqdm import tqdm
import json
import time

def load_amazon_data():
    """Load Amazon dataset"""
    print("📊 Loading Amazon data...")
    
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    print(f"Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
    return train_df, val_df, test_df

# Simple AdaGIN Implementation
class AmazonDataset(Dataset):
    def __init__(self, df):
        self.data = df
        
        # Prepare features
        self.features = []
        self.labels = df['label'].values
        
        for _, row in df.iterrows():
            feature_vector = [
                int(row['user_id']),
                int(row['item_id']),
                int(row['brand_index']),
                int(row['price_range_index']),
                int(row['category_1']),
                int(row['category_2']),
                int(row['category_3']),
                float(row['price'])
            ]
            self.features.append(feature_vector)
        
        self.features = np.array(self.features)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return {
            'features': torch.tensor(self.features[idx], dtype=torch.float32),
            'label': torch.tensor(self.labels[idx], dtype=torch.float32)
        }

class SimpleAdaGIN(nn.Module):
    def __init__(self, feature_dims, embedding_dim=16):
        super().__init__()
        
        # Embedding layers for categorical features
        self.embeddings = nn.ModuleList()
        for dim in feature_dims:
            self.embeddings.append(nn.Embedding(dim + 10, embedding_dim))  # +10 for safety
        
        # MLP for final prediction
        total_dim = len(feature_dims) * embedding_dim + 1  # +1 for price
        self.mlp = nn.Sequential(
            nn.Linear(total_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1)
        )
    
    def forward(self, features):
        embedded_features = []
        
        # Embed categorical features
        for i, embedding in enumerate(self.embeddings):
            cat_feature = features[:, i].long()
            # Clamp to avoid out-of-bounds
            cat_feature = torch.clamp(cat_feature, 0, embedding.num_embeddings - 1)
            embedded = embedding(cat_feature)
            embedded_features.append(embedded)
        
        # Add numeric feature (price)
        price = features[:, -1:]
        embedded_features.append(price)
        
        # Concatenate all features
        x = torch.cat(embedded_features, dim=1)
        
        # Final prediction
        output = self.mlp(x)
        return output.squeeze()

class SimpleUniSRec(nn.Module):
    def __init__(self, num_users, num_items, embedding_dim=64):
        super().__init__()
        
        self.user_embedding = nn.Embedding(num_users + 10, embedding_dim)
        self.item_embedding = nn.Embedding(num_items + 10, embedding_dim)
        
        # Simple MLP for prediction
        self.mlp = nn.Sequential(
            nn.Linear(embedding_dim * 2, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 1)
        )
    
    def forward(self, features):
        user_ids = features[:, 0].long()
        item_ids = features[:, 1].long()
        
        # Clamp to avoid out-of-bounds
        user_ids = torch.clamp(user_ids, 0, self.user_embedding.num_embeddings - 1)
        item_ids = torch.clamp(item_ids, 0, self.item_embedding.num_embeddings - 1)
        
        user_emb = self.user_embedding(user_ids)
        item_emb = self.item_embedding(item_ids)
        
        # Concatenate embeddings
        combined = torch.cat([user_emb, item_emb], dim=1)
        
        # Predict rating
        output = self.mlp(combined)
        return output.squeeze()

def train_model(model, train_loader, val_loader, test_loader, model_name, epochs=3):
    """Train a model and return results"""
    print(f"\n🚀 Training {model_name}...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.BCEWithLogitsLoss()
    
    # Training loop
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            features = batch['features'].to(device)
            labels = batch['label'].to(device)
            
            outputs = model(features)
            loss = criterion(outputs, labels)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        print(f"Epoch {epoch+1} Loss: {total_loss/len(train_loader):.4f}")
    
    # Evaluation
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            features = batch['features'].to(device)
            labels = batch['label'].cpu().numpy()
            
            outputs = model(features)
            preds = torch.sigmoid(outputs).cpu().numpy()
            
            all_preds.extend(preds)
            all_labels.extend(labels)
    
    # Calculate metrics
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)
    
    # Clip predictions to avoid numerical issues
    all_preds = np.clip(all_preds, 1e-6, 1 - 1e-6)
    
    auc = roc_auc_score(all_labels, all_preds)
    logloss = log_loss(all_labels, all_preds)
    accuracy = accuracy_score(all_labels, (all_preds >= 0.5).astype(int))
    
    results = {
        'model': model_name,
        'test_auc': float(auc),
        'test_logloss': float(logloss),
        'test_accuracy': float(accuracy)
    }
    
    print(f"✅ {model_name} Results:")
    print(f"   Test AUC: {auc:.4f}")
    print(f"   Test Logloss: {logloss:.4f}")
    print(f"   Test Accuracy: {accuracy:.4f}")
    
    return results

def main():
    print("🎯 Simple Baseline Training: AdaGIN, UniSRec")
    print("=" * 50)
    
    # Load data
    train_df, val_df, test_df = load_amazon_data()
    
    # Create datasets
    train_dataset = AmazonDataset(train_df)
    val_dataset = AmazonDataset(val_df)
    test_dataset = AmazonDataset(test_df)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=512)
    test_loader = DataLoader(test_dataset, batch_size=512)
    
    # Get feature dimensions
    max_user = max(train_df['user_id'].max(), val_df['user_id'].max(), test_df['user_id'].max())
    max_item = max(train_df['item_id'].max(), val_df['item_id'].max(), test_df['item_id'].max())
    max_brand = max(train_df['brand_index'].max(), val_df['brand_index'].max(), test_df['brand_index'].max())
    max_price_range = max(train_df['price_range_index'].max(), val_df['price_range_index'].max(), test_df['price_range_index'].max())
    max_cat1 = max(train_df['category_1'].max(), val_df['category_1'].max(), test_df['category_1'].max())
    max_cat2 = max(train_df['category_2'].max(), val_df['category_2'].max(), test_df['category_2'].max())
    max_cat3 = max(train_df['category_3'].max(), val_df['category_3'].max(), test_df['category_3'].max())
    
    feature_dims = [max_user, max_item, max_brand, max_price_range, max_cat1, max_cat2, max_cat3]
    
    all_results = []
    
    # Train AdaGIN
    adagin_model = SimpleAdaGIN(feature_dims)
    adagin_results = train_model(adagin_model, train_loader, val_loader, test_loader, "AdaGIN")
    all_results.append(adagin_results)
    
    # Train UniSRec
    unisrec_model = SimpleUniSRec(max_user + 1, max_item + 1)
    unisrec_results = train_model(unisrec_model, train_loader, val_loader, test_loader, "UniSRec")
    all_results.append(unisrec_results)
    
    # Print final summary
    print(f"\n{'='*50}")
    print("📊 FINAL RESULTS")
    print(f"{'='*50}")
    print(f"{'Model':<12} {'AUC':<8} {'Logloss':<8} {'Accuracy':<10}")
    print("-" * 45)
    
    for result in all_results:
        model = result['model']
        auc = result['test_auc']
        logloss = result['test_logloss']
        accuracy = result['test_accuracy']
        
        print(f"{model:<12} {auc:<8.4f} {logloss:<8.4f} {accuracy:<10.4f}")
    
    # Save results
    with open('simple_baselines_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n📁 Results saved to: simple_baselines_results.json")
    
    return all_results

if __name__ == "__main__":
    results = main()
