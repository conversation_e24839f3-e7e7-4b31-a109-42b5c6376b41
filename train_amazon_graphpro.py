#!/usr/bin/env python3
"""
Train GraphPro model on Amazon dataset for comparative study
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime

# Add GraphPro to path
sys.path.append('GraphLLM4CTR_baseline/GraphPro')

def prepare_amazon_data_for_graphpro():
    """Prepare Amazon data for GraphPro training"""
    print("📊 Preparing Amazon data for GraphPro...")
    
    # Load the Amazon data with split categories
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    print(f"Train shape: {train_df.shape}")
    print(f"Val shape: {val_df.shape}")
    print(f"Test shape: {test_df.shape}")
    
    # GraphPro expects user-item interaction format
    # Convert to GraphPro format: user_id, item_id, rating, timestamp
    
    # Create output directory
    output_dir = 'GraphLLM4CTR_baseline/GraphPro/dataset/amazon'
    os.makedirs(output_dir, exist_ok=True)
    
    # Convert to GraphPro format
    def convert_to_graphpro_format(df, split_name):
        # GraphPro expects: user_id, item_id, rating (0/1), timestamp
        graphpro_df = pd.DataFrame({
            'user_id': df['user_id'],
            'item_id': df['item_id'], 
            'rating': df['label'],  # Use label as rating (0/1)
            'timestamp': range(len(df))  # Dummy timestamp
        })
        
        # Save in GraphPro format
        graphpro_df.to_csv(f'{output_dir}/{split_name}.csv', index=False, sep='\t')
        return graphpro_df
    
    train_graphpro = convert_to_graphpro_format(train_df, 'train')
    val_graphpro = convert_to_graphpro_format(val_df, 'val')
    test_graphpro = convert_to_graphpro_format(test_df, 'test')
    
    # Create dataset info file
    dataset_info = {
        'dataset_name': 'amazon',
        'num_users': max(train_df['user_id'].max(), val_df['user_id'].max(), test_df['user_id'].max()) + 1,
        'num_items': max(train_df['item_id'].max(), val_df['item_id'].max(), test_df['item_id'].max()) + 1,
        'num_interactions': len(train_df) + len(val_df) + len(test_df)
    }
    
    with open(f'{output_dir}/info.json', 'w') as f:
        json.dump(dataset_info, f, indent=2)
    
    print(f"✅ GraphPro data saved to {output_dir}")
    print(f"📊 Dataset info: {dataset_info}")
    
    return output_dir

def train_amazon_graphpro():
    """Train GraphPro on Amazon dataset"""
    print("🚀 Starting GraphPro training on Amazon dataset...")
    
    # Prepare data
    data_dir = prepare_amazon_data_for_graphpro()
    if data_dir is None:
        print("❌ Data preparation failed!")
        return
    
    # Import GraphPro modules
    try:
        from GraphLLM4CTR_baseline.GraphPro.finetune import main as graphpro_main
        from GraphLLM4CTR_baseline.GraphPro.utils.parse_args import parse_args
    except ImportError as e:
        print(f"❌ Failed to import GraphPro modules: {e}")
        return None
    
    # Configure GraphPro arguments
    args_list = [
        '--dataset', 'amazon',
        '--data_path', data_dir,
        '--output_dir', 'GraphLLM4CTR_baseline/GraphPro/output_amazon_graphpro',
        '--epochs', '10',
        '--batch_size', '1024',
        '--lr', '0.001',
        '--embedding_size', '64',
        '--hidden_size', '128',
        '--num_layers', '2',
        '--dropout', '0.2',
        '--seed', '2023',
        '--device', 'cuda' if torch.cuda.is_available() else 'cpu'
    ]
    
    # Train the model
    try:
        # Parse arguments
        args = parse_args(args_list)
        
        # Run GraphPro training
        results = graphpro_main(args)
        
        # Save results
        output_dir = args.output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        results_dict = {
            'model': 'GraphPro',
            'dataset': 'Amazon',
            'timestamp': datetime.now().isoformat(),
            'test_auc': results.get('test_auc', 0.0),
            'test_logloss': results.get('test_logloss', 0.0),
            'test_accuracy': results.get('test_accuracy', 0.0)
        }
        
        with open(f'{output_dir}/results.json', 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        print("✅ GraphPro training completed!")
        print(f"📊 Results: AUC={results_dict['test_auc']:.4f}, "
              f"Logloss={results_dict['test_logloss']:.4f}")
        
        return results_dict
        
    except Exception as e:
        print(f"❌ GraphPro training failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    train_amazon_graphpro()
