#!/usr/bin/env python3
"""
Run all working baseline models on Amazon dataset
"""

import os
import subprocess
import time
import json
from datetime import datetime

def run_command(cmd, timeout=1800):
    """Run a command with timeout"""
    try:
        start_time = time.time()
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        end_time = time.time()
        
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'time_seconds': end_time - start_time
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'error': 'Timeout',
            'time_seconds': timeout
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'time_seconds': 0
        }

def main():
    print("🎯 Amazon Dataset Baseline Models Training")
    print("=" * 60)
    
    # List of working models
    models = [
        {
            'name': 'DCNv2',
            'command': 'cd GraphLLM4CTR_baseline/DCNv2 && python train_amazon.py --num_epochs 5',
            'timeout': 600  # 10 minutes
        },
        {
            'name': 'GraphPro', 
            'command': 'cd GraphLLM4CTR_baseline/GraphPro && python train_amazon_graphpro.py --num_epochs 5',
            'timeout': 900  # 15 minutes
        },
        {
            'name': 'LR',
            'command': 'cd ctr-metrics-eval && python scripts/train.py --dataset_name AmazonCTR --dataset_path /data/datasets/processed_datasets/amazon --model_name LR --model_output_path output_amazon/ --epochs 5 --batch_size 256 --embedding_dim 16 --learning_rate 0.001 --loss binary_crossentropy --optimizer adam --monitor_mode max --patience 2 --regularizer 0.00012 --task binary_classification --every_x_epochs 1 --seed 2023',
            'timeout': 600
        },
        {
            'name': 'FM',
            'command': 'cd ctr-metrics-eval && python scripts/train.py --dataset_name AmazonCTR --dataset_path /data/datasets/processed_datasets/amazon --model_name FM --model_output_path output_amazon/ --epochs 5 --batch_size 256 --embedding_dim 16 --learning_rate 0.001 --loss binary_crossentropy --optimizer adam --monitor_mode max --patience 2 --regularizer 0.00012 --task binary_classification --every_x_epochs 1 --seed 2023',
            'timeout': 600
        },
        {
            'name': 'DeepFM',
            'command': 'cd ctr-metrics-eval && python scripts/train.py --dataset_name AmazonCTR --dataset_path /data/datasets/processed_datasets/amazon --model_name DeepFM --model_output_path output_amazon/ --epochs 5 --batch_size 256 --embedding_dim 16 --learning_rate 0.001 --loss binary_crossentropy --optimizer adam --monitor_mode max --patience 2 --regularizer 0.00012 --task binary_classification --every_x_epochs 1 --seed 2023',
            'timeout': 900
        }
    ]
    
    results = []
    total_start_time = time.time()
    
    for model in models:
        print(f"\n🚀 Training {model['name']}...")
        print(f"Command: {model['command']}")
        
        result = run_command(model['command'], model['timeout'])
        
        model_result = {
            'model': model['name'],
            'success': result['success'],
            'time_minutes': result['time_seconds'] / 60,
            'timestamp': datetime.now().isoformat()
        }
        
        if result['success']:
            print(f"✅ {model['name']} completed in {result['time_seconds']/60:.2f} minutes")
            
            # Try to extract metrics from output
            stdout = result['stdout']
            if 'Test AUC' in stdout:
                # Extract AUC and other metrics
                lines = stdout.split('\n')
                for line in lines:
                    if 'Test AUC' in line:
                        try:
                            auc = float(line.split('Test AUC')[1].split(':')[1].split()[0])
                            model_result['test_auc'] = auc
                        except:
                            pass
                    if 'Test Logloss' in line:
                        try:
                            logloss = float(line.split('Test Logloss')[1].split(':')[1].split()[0])
                            model_result['test_logloss'] = logloss
                        except:
                            pass
        else:
            print(f"❌ {model['name']} failed")
            if 'error' in result:
                print(f"Error: {result['error']}")
                model_result['error'] = result['error']
            else:
                print(f"Error: {result['stderr'][:200]}...")
                model_result['error'] = result['stderr'][:200]
        
        results.append(model_result)
    
    total_time = time.time() - total_start_time
    
    # Create summary
    print(f"\n{'='*60}")
    print("📊 AMAZON BASELINE TRAINING SUMMARY")
    print(f"{'='*60}")
    print(f"Total time: {total_time/3600:.2f} hours")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print(f"\n📊 RESULTS TABLE:")
        print(f"{'Model':<12} {'AUC':<8} {'Logloss':<8} {'Time (min)':<12}")
        print("-" * 45)
        
        for result in successful:
            auc = result.get('test_auc', 'N/A')
            logloss = result.get('test_logloss', 'N/A')
            time_min = result['time_minutes']
            
            auc_str = f"{auc:.4f}" if isinstance(auc, float) else str(auc)
            logloss_str = f"{logloss:.4f}" if isinstance(logloss, float) else str(logloss)
            
            print(f"{result['model']:<12} {auc_str:<8} {logloss_str:<8} {time_min:<12.2f}")
    
    if failed:
        print(f"\n❌ Failed models:")
        for result in failed:
            print(f"  - {result['model']}: {result.get('error', 'Unknown error')}")
    
    # Save results
    output_file = 'amazon_baseline_results.json'
    summary = {
        'experiment': 'Amazon Baseline Models',
        'timestamp': datetime.now().isoformat(),
        'total_time_hours': total_time / 3600,
        'successful_models': len(successful),
        'failed_models': len(failed),
        'results': results
    }
    
    with open(output_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📁 Results saved to: {output_file}")

if __name__ == "__main__":
    main()
