import pandas as pd
import gzip
import json
from tqdm import tqdm
from datetime import datetime
import os
import re
import unicodedata
import html
import string
from functools import wraps
from urllib.parse import unquote

import pkg_resources
import plane
from plane.pattern import Regex

import ast

tqdm.pandas()

def load_reviews_json(file_path):
    """
    Load the Sports_and_Outdoors_5.json file.
    Each line is a separate JSON object containing review data.
    """
    reviews = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading reviews"):
            try:
                review = json.loads(line.strip())
                reviews.append(review)
            except json.JSONDecodeError as e:
                print(f"Error parsing line: {e}")
                continue
    
    return reviews

def load_metadata_json(file_path):
    """
    Load the meta_Sports_and_Outdoors.json file (gzipped).
    Each line is a separate JSON object containing product metadata.
    """
    metadata = []
    
    with gzip.open(file_path, 'rt', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading metadata"):
            try:
                meta = json.loads(line.strip())
                metadata.append(meta)
            except json.JSONDecodeError as e:
                print(f"Error parsing line: {e}")
                continue
    
    return metadata

# Load 5-core
reviews = load_reviews_json('/data/datasets/amazon/Sports_and_Outdoors_5.json')
print(f"Loaded {len(reviews)} reviews")

# Convert to DataFrame
reviews_df = pd.DataFrame(reviews)
# Remove useless columns
reviews_df = reviews_df.drop(columns=['style','vote','image','summary','reviewText'])
# Process time
reviews_df['unixReviewTime_convert'] = reviews_df['unixReviewTime'].apply(lambda x: datetime.utcfromtimestamp(x))
print(f"DataFrame shape: {reviews_df.shape}")
print(f"Columns: {list(reviews_df.columns)}")
reviews_df.head()

reviews_df = reviews_df.drop_duplicates()
print(f"Remove Duplicates DataFrame shape: {reviews_df.shape}")

# Keep useful columns
reviews_df = reviews_df[['overall','reviewerID','asin','unixReviewTime_convert']]
print(f"Keep Useful Columns: {list(reviews_df.columns)}")
reviews_df.head()

reviews_df['overall'].value_counts()

# Load metadata
metadata = load_metadata_json('/data/datasets/amazon/meta_Sports_and_Outdoors.json')
print(f"Loaded {len(metadata)} metadata entries")

# Convert metadata to DataFrame
metadata_df = pd.DataFrame(metadata)
print(f"Metadata DataFrame shape: {metadata_df.shape}")
print(f"Metadata columns: {list(metadata_df.columns)}")

# Keep useful columns
metadata_df = metadata_df[['asin','title','price','category','brand']]
metadata_df['category'] = metadata_df['category'].astype(str)
metadata_df = metadata_df.drop_duplicates()

print(reviews_df.shape)
print(metadata_df.shape)

# Merge data
reviews_df['asin'] = reviews_df['asin'].astype(str)
metadata_df['asin'] = metadata_df['asin'].astype(str)
df_merge = pd.merge(reviews_df, metadata_df, on='asin', how='left')
print(df_merge.shape)

# Remove dirty data
df_merge_clean = df_merge[~df_merge['title'].isna()]
print('remove title nan', df_merge_clean.shape)

df_merge_clean = df_merge_clean[~df_merge_clean['price'].isna()]
print('remove price nan', df_merge_clean.shape)

df_merge_clean = df_merge_clean[~df_merge_clean['brand'].isna()]
print('remove brand nan', df_merge_clean.shape)

df_merge_clean = df_merge_clean[~df_merge_clean['category'].isna()]
print('remove category nan', df_merge_clean.shape)

def verbose(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        self = func(self, *args, **kwargs)
        # if self.verbose:
        #     print('[DEBUG] After {:20} => "{}"'.format(
        #         func.__name__, self._text))
        return self
    return wrapper

def replace_func(_text):
    _text = _text.group(1) + " " + _text.group(3)
    return _text

class SouthAmericaLan:
    """text preprocessing.

    :param bool verbose: activate DEBUG model
    :param dict norm_map: use customized punctuation normalization mappings
    :param str segmenter: use custumized segmenter
    """
    def __init__(self, verbose=False, norm_map=None, segmenter=None):
        self._text = ''
        self.verbose = verbose
        self.punc = plane.Punctuation(norm_map) if norm_map else plane.punc
        self.plane = plane.Plane()
        self.segmenter = segmenter
        self.british2american = None
        self.tag_map = {
            'html': plane.HTML,
            'email': plane.EMAIL,
            'space': plane.SPACE,
            'url': plane.URL,
        }
        self.lang = {
            'TW': plane.CHINESE,
            'VN': plane.VIETNAMESE,
            'TH': plane.THAI,
            'EN': plane.ENGLISH,
            'PT': plane.BraSCII,
            'ES': plane.BraSCII,
            'NUM': plane.NUMBER,
        }
        self.pattern1 = re.compile(r"""([a-z]+)(-)([a-z])""", re.VERBOSE)
        self.pattern2 = re.compile(r"""([a-z]+)(\.)([a-z])""", re.VERBOSE)

    def update(self, text):
        self._text = text
        return self

    @property
    def text(self):
        return self._text


    @text.setter
    def text(self, text):
        self._text = text

    def init_english_map(self, file=None):
        if not file:
            file = os.path.join(
                pkg_resources.resource_filename('data_clean_module_dev','data/assets'),
                'british_american.json'
            )

        with open(file) as f:
            self.british2american = json.load(f)

    @verbose
    def american(self):
        """Transfer British English to American English
        """
        if not self.british2american:
            self.init_english_map()
        self._text = ' '.join([self.british2american.get(word.lower(), word)
                               for word in self._text.split(' ')])
        return self


    @verbose
    def normalize_unicode(self, form='NFC'):
        """Unicode Normalization.

        :param str form: Unicode format, 'NFC', 'NFKC', 'NFD', 'NFKD'

        For more information:

        - http://unicode.org/reports/tr15/
        - https://docs.python.org/3.7/library/unicodedata.html
        - https://unicode.org/charts/normalization/
        """
        self._text = unicodedata.normalize(form, self._text)
        return self

    @verbose
    def normalize_punctuation(self):
        """Transfer punctuations from other languages to English punctuations.
        """
        self._text = self.punc.normalize(self._text)
        return self
    
    @verbose
    def remove_special_punctuation(self):
        self._text = self.pattern1.sub(replace_func, self._text)
        self._text = self.pattern2.sub(replace_func, self._text)
        return self

    @verbose
    def remove_punctuation(self, white_list=["-", ".", "+", "&"]):
        """Remove all the punctuations belongs to Unicode::Category::[P]

        - https://www.compart.com/en/unicode/category/Po
        """
        
        remove_punc = [item for item in string.punctuation if item not in white_list]
        self._text = "".join([item if item not in remove_punc else " " for item in self._text])
        self._text = " ".join(self._text.split())
        return self

    @verbose
    def unescape_html(self):
        """HTML Escape

        - https://dev.w3.org/html5/html-author/charref
        - https://www.freeformatter.com/html-entities.html
        """
        self._text = html.unescape(self._text)
        return self


    @verbose
    def unquote_url(self):
        """URL unquote
        """
        self._text = unquote(self._text)
        return self


    @verbose
    def filter_lang(self, lang=['TW', 'EN', 'VN', 'NUM', 'TH', 'PT','ES']):
        """Extract specific language characters from text.

        :param list[str] lang: ['TW', 'EN', 'VN', 'TH', 'NUM']
        """
        for lan in lang:
            if lan not in self.lang:
                raise NameError('Unknown lang: {}. Only support {}.'.format(
                    lan, self.lang.keys()))

        regex = sum([self.lang.get(lan) for lan in lang] + [plane.SPACE])
        self._text = ''.join([t.value for t in
                              plane.extract(self._text, regex)])
        return self

    @verbose
    def remove_tag(self, tag=['html', 'url', 'email', 'space']):
        """The order of tags matters.
        :param list[str] tag: ['html', 'url', 'email', 'space']
        """
        if not tag:
            return self
        for t in tag:
            if t not in self.tag_map:
                raise NameError('Unknown tag: {}. Only support {}.'.format(
                    t, self.tag_map.keys()))

            self._text = plane.replace(self._text, self.tag_map[t])
        return self

    @verbose
    def segment(self, region='PT'):
        # self._text = plane.segment(self._text)
        self._text = self._text.split()
        return self

    def lower_case(self, region='PT'):
        return self._text.lower()


    def preprocess(self, text, lan='PT'):
        """All-in-one method. It's suitable for the common circumstance

        :param str text: text
        :param str country: ['TW', 'TH', 'SG', 'MY', 'ID', 'VN', 'PH']
        """
        return (self.update(text)
                .normalize_unicode()
                .unescape_html()
                .american()
                .filter_lang([lan, 'EN', 'NUM']
                             if lan in ['TW', 'VN', 'TH', 'PT','ES']
                             else ['EN', 'NUM'])
                .remove_tag(['html', 'url', 'email', 'space'])
                .remove_special_punctuation()
                .remove_punctuation()
                .lower_case()
                )

class AmazonTitleCleaner:
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.preprocessor = SouthAmericaLan(verbose=verbose)
        
    def clean_title(self, title):
        """
        Clean Amazon product titles by:
        1. Removing HTML entities
        2. Normalizing unicode
        3. Removing excessive punctuation
        4. Standardizing spacing
        5. Converting to lowercase
        """
        if not title or pd.isna(title):
            return ""
            
        # Basic cleaning
        title = str(title).strip()
        
        # Process with our text preprocessor
        clean_title = (self.preprocessor.update(title)
                      .normalize_unicode()
                      .unescape_html()
                      .normalize_punctuation()
                      .remove_special_punctuation()
                      .remove_punctuation(white_list=["-", ".", "+", "&"])
                      .lower_case())
        
        # Remove extra spaces
        clean_title = " ".join(clean_title.split())
        
        # if self.verbose:
        #     print(f"Original: {title}")
        #     print(f"Cleaned:  {clean_title}")
            
        return clean_title
        
    def process_titles(self, df, title_col='title'):
        """Process all titles in a dataframe"""
        df['clean_title'] = df[title_col].progress_apply(self.clean_title)
        return df

# Process feature
# Process title
title_cleaner = AmazonTitleCleaner(verbose=True)
df_merge_clean = title_cleaner.process_titles(df_merge_clean)

# Process price
df_merge_clean['price_clean'] = df_merge_clean['price'].apply(lambda x: str(x).replace('$',''))
df_merge_clean = df_merge_clean[df_merge_clean['price_clean']!='']
print(df_merge_clean.shape)

# Process brand
df_merge_clean = df_merge_clean[df_merge_clean['brand'] != '']
print(df_merge_clean.shape)

# Process category
df_merge_clean = df_merge_clean[df_merge_clean['category'] != '[]']
print(df_merge_clean.shape)

df_merge_clean['category'] = df_merge_clean['category'].apply(lambda x: ast.literal_eval(x))

df_merge_clean['category_clean'] = df_merge_clean['category'].apply(lambda x: "-".join(str(i) for i in x))

df_merge_clean = df_merge_clean[['overall','reviewerID','asin','unixReviewTime_convert','clean_title','category_clean','price_clean','brand']]

df_merge_clean.shape

df_merge_clean = df_merge_clean.sort_values(by='unixReviewTime_convert')
df_merge_clean = df_merge_clean.reset_index(drop=True)

df_merge_clean['label'] = df_merge_clean['overall'].apply(lambda x: 1 if x > 4 else 0)

df_merge_clean.tail()

df_merge_clean.to_csv('/data/datasets/amazon/amazon_process.csv', index=False)

# Basic statistics
print("=== REVIEWS STATISTICS ===")
print(f"Total reviews: {len(reviews_df)}")
print(f"Unique users: {reviews_df['reviewerID'].nunique()}")
print(f"Unique items: {reviews_df['asin'].nunique()}")
print(f"Rating distribution:")
print(reviews_df['overall'].value_counts().sort_index())