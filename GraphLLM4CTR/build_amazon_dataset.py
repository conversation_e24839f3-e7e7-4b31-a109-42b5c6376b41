import pandas as pd
import os
import numpy as np
from datetime import datetime
import ast
import re
from sklearn.model_selection import train_test_split

def load_amazon_data(data_path):
    """Load Amazon dataset from processed CSV file"""
    print("Loading Amazon dataset...")
    df = pd.read_csv(data_path)
    print(f"Loaded {len(df)} records")
    print(f"Columns: {list(df.columns)}")
    return df

def preprocess_amazon_features(df):
    """Preprocess Amazon-specific features"""
    print("Preprocessing Amazon features...")

    # Rename columns to match the pipeline convention
    df = df.rename(columns={
        'reviewerID': 'user_id',
        'asin': 'item_id',
        'unixReviewTime_convert': 'datetime',
        'clean_title': 'title',
        'category_clean': 'category',
        'price_clean': 'price'
    })

    # Convert datetime
    df['datetime'] = pd.to_datetime(df['datetime'])

    # Process price - handle ranges like "14.95 - 88.61"
    def process_price(price_str):
        if pd.isna(price_str):
            return 0.0
        price_str = str(price_str)
        # Extract first price if it's a range
        if ' - ' in price_str:
            price_str = price_str.split(' - ')[0]
        try:
            return float(price_str)
        except:
            return 0.0

    df['price'] = df['price'].apply(process_price)

    # Process category - split into main categories
    def process_category(cat_str):
        if pd.isna(cat_str):
            return []
        # Split by '-' and clean up
        categories = [cat.strip() for cat in str(cat_str).split('-')]
        return categories[:3]  # Take first 3 levels to avoid too many categories

    df['category_list'] = df['category'].apply(process_category)

    # Process brand
    df['brand'] = df['brand'].fillna('Unknown')

    print(f"Processed dataset shape: {df.shape}")
    return df

def create_amazon_mappings(df, save_path):
    """Create ID mappings for Amazon dataset"""
    print("Creating ID mappings...")

    # User ID mapping
    unique_users = sorted(df['user_id'].unique())
    user_mapping = pd.DataFrame({
        'user_id': unique_users,
        'user_index': range(len(unique_users))
    })

    # Item ID mapping
    unique_items = sorted(df['item_id'].unique())
    item_mapping = pd.DataFrame({
        'item_id': unique_items,
        'item_index': range(len(unique_items))
    })

    # Brand mapping
    unique_brands = sorted(df['brand'].unique())
    brand_mapping = pd.DataFrame({
        'brand': unique_brands,
        'brand_index': range(1, len(unique_brands) + 1)  # Start from 1, 0 for padding
    })

    # Category mapping
    all_categories = []
    for cat_list in df['category_list']:
        all_categories.extend(cat_list)
    unique_categories = sorted(list(set(all_categories)))
    category_mapping = pd.DataFrame({
        'category': unique_categories,
        'category_index': range(1, len(unique_categories) + 1)  # Start from 1, 0 for padding
    })

    # Price range mapping (discretize prices)
    price_ranges = [0, 10, 25, 50, 100, 200, float('inf')]
    price_labels = ['0-10', '10-25', '25-50', '50-100', '100-200', '200+']
    df['price_range'] = pd.cut(df['price'], bins=price_ranges, labels=price_labels, include_lowest=True)

    unique_price_ranges = df['price_range'].cat.categories.tolist()
    price_range_mapping = pd.DataFrame({
        'price_range': unique_price_ranges,
        'price_range_index': range(1, len(unique_price_ranges) + 1)
    })

    # Save mappings
    os.makedirs(save_path, exist_ok=True)
    user_mapping.to_csv(os.path.join(save_path, 'user_mapping.csv'), index=False)
    item_mapping.to_csv(os.path.join(save_path, 'item_mapping.csv'), index=False)
    brand_mapping.to_csv(os.path.join(save_path, 'brand_mapping.csv'), index=False)
    category_mapping.to_csv(os.path.join(save_path, 'category_mapping.csv'), index=False)
    price_range_mapping.to_csv(os.path.join(save_path, 'price_range_mapping.csv'), index=False)

    print(f"Created mappings:")
    print(f"  Users: {len(unique_users)}")
    print(f"  Items: {len(unique_items)}")
    print(f"  Brands: {len(unique_brands)}")
    print(f"  Categories: {len(unique_categories)}")
    print(f"  Price ranges: {len(unique_price_ranges)}")

    return df, user_mapping, item_mapping, brand_mapping, category_mapping, price_range_mapping

def apply_amazon_mappings(df, user_mapping, item_mapping, brand_mapping, category_mapping, price_range_mapping):
    """Apply ID mappings to the dataset"""
    print("Applying ID mappings...")

    # Create mapping dictionaries
    user_map = dict(zip(user_mapping['user_id'], user_mapping['user_index']))
    item_map = dict(zip(item_mapping['item_id'], item_mapping['item_index']))
    brand_map = dict(zip(brand_mapping['brand'], brand_mapping['brand_index']))
    category_map = dict(zip(category_mapping['category'], category_mapping['category_index']))
    price_range_map = dict(zip(price_range_mapping['price_range'], price_range_mapping['price_range_index']))

    # Apply mappings
    df['user_id'] = df['user_id'].map(user_map)
    df['item_id'] = df['item_id'].map(item_map)
    df['brand_index'] = df['brand'].map(brand_map)
    df['price_range_index'] = df['price_range'].map(price_range_map)

    # Process category indices
    def map_categories(cat_list):
        if not cat_list:
            return [0]  # Padding
        mapped = [category_map.get(cat, 0) for cat in cat_list]
        # Pad to fixed length (3)
        while len(mapped) < 3:
            mapped.append(0)
        return mapped[:3]

    df['category_indices'] = df['category_list'].apply(map_categories)

    return df

def filter_cold_start_amazon(df, min_user_interactions=5, min_item_interactions=5):
    """Filter out cold-start users and items to ensure proper graph construction"""
    print(f"Filtering cold-start data (min_user_interactions={min_user_interactions}, min_item_interactions={min_item_interactions})...")

    original_size = len(df)
    original_users = df['user_id'].nunique()
    original_items = df['item_id'].nunique()

    # Iteratively filter until convergence
    prev_size = 0
    iteration = 0

    while len(df) != prev_size:
        iteration += 1
        prev_size = len(df)

        # Count interactions per user and item
        user_counts = df['user_id'].value_counts()
        item_counts = df['item_id'].value_counts()

        # Filter users with sufficient interactions
        valid_users = user_counts[user_counts >= min_user_interactions].index
        df = df[df['user_id'].isin(valid_users)]

        # Filter items with sufficient interactions
        valid_items = item_counts[item_counts >= min_item_interactions].index
        df = df[df['item_id'].isin(valid_items)]

        print(f"  Iteration {iteration}: {len(df)} interactions, {df['user_id'].nunique()} users, {df['item_id'].nunique()} items")

    final_users = df['user_id'].nunique()
    final_items = df['item_id'].nunique()

    print(f"Cold-start filtering results:")
    print(f"  Interactions: {original_size} → {len(df)} ({len(df)/original_size:.1%} retained)")
    print(f"  Users: {original_users} → {final_users} ({final_users/original_users:.1%} retained)")
    print(f"  Items: {original_items} → {final_items} ({final_items/original_items:.1%} retained)")

    return df

def split_amazon_dataset(df, test_size=0.1, val_size=0.1, random_state=42):
    """Split Amazon dataset into train/val/test sets with cold-start handling"""
    print("Splitting dataset with cold-start handling...")

    # Group by user and split each user's interactions temporally
    train_data = []
    val_data = []
    test_data = []

    # Sort by datetime for temporal split
    df_sorted = df.sort_values(['user_id', 'datetime']).reset_index(drop=True)

    # Group by user_id and process each user's interactions
    for user_id, user_data in df_sorted.groupby('user_id'):
        # Sort user's interactions by time
        user_data = user_data.sort_values('datetime')
        n_interactions = len(user_data)

        # Calculate split points to achieve roughly 80:10:10 (train:val:test)
        n_train = max(1, int(0.8 * n_interactions))  # At least 1 for training
        n_val = max(0, int(0.1 * n_interactions))    # Could be 0 for very few interactions

        if n_interactions >= 10:  # Enough interactions for proper split
            train_data.append(user_data.iloc[:n_train])
            if n_val > 0:
                val_data.append(user_data.iloc[n_train:n_train+n_val])
            test_data.append(user_data.iloc[n_train+n_val:])
        elif n_interactions >= 3:  # Moderate number of interactions
            train_data.append(user_data.iloc[:-2])
            val_data.append(user_data.iloc[-2:-1])
            test_data.append(user_data.iloc[-1:])
        elif n_interactions == 2:  # Only two interactions
            train_data.append(user_data.iloc[:1])
            val_data.append(user_data.iloc[1:])
        else:  # Single interaction - put in training
            train_data.append(user_data)

    # Combine all data
    train_data = pd.concat(train_data, ignore_index=True) if train_data else pd.DataFrame()
    val_data = pd.concat(val_data, ignore_index=True) if val_data else pd.DataFrame()
    test_data = pd.concat(test_data, ignore_index=True) if test_data else pd.DataFrame()

    # Get training users and items sets
    train_users = set(train_data['user_id'].unique())
    train_items = set(train_data['item_id'].unique())

    print(f"Training set coverage:")
    print(f"  Users: {len(train_users)}")
    print(f"  Items: {len(train_items)}")

    # Filter validation and test sets to remove cold-start items and users
    print("Filtering validation and test sets for cold-start...")

    # Count before filtering
    val_before = len(val_data)
    test_before = len(test_data)

    # Remove interactions with items not in training
    val_data = val_data[val_data['item_id'].isin(train_items)]
    test_data = test_data[test_data['item_id'].isin(train_items)]

    # Remove interactions with users not in training
    val_data = val_data[val_data['user_id'].isin(train_users)]
    test_data = test_data[test_data['user_id'].isin(train_users)]

    # Count after filtering
    val_removed = val_before - len(val_data)
    test_removed = test_before - len(test_data)

    print(f"Cold-start filtering results:")
    print(f"  Validation: removed {val_removed} samples ({val_removed/val_before*100:.1f}% of validation set)")
    print(f"  Test: removed {test_removed} samples ({test_removed/test_before*100:.1f}% of test set)")

    n_total = len(train_data) + len(val_data) + len(test_data)
    print(f"Final dataset split:")
    print(f"  Train: {len(train_data)} ({len(train_data)/n_total:.1%})")
    print(f"  Val: {len(val_data)} ({len(val_data)/n_total:.1%})")
    print(f"  Test: {len(test_data)} ({len(test_data)/n_total:.1%})")

    return train_data, val_data, test_data

def create_final_amazon_mappings(train_data, val_data, test_data, save_path):
    """Create final mappings based on train/val/test data"""
    print("Creating final mappings based on actual data splits...")

    # Combine all data to ensure consistent mappings
    all_data = pd.concat([train_data, val_data, test_data], ignore_index=True)

    # Create mappings
    all_data_with_price_range, user_mapping, item_mapping, brand_mapping, category_mapping, price_range_mapping = create_amazon_mappings(all_data, save_path)

    # Add price_range to individual splits
    price_ranges = [0, 10, 25, 50, 100, 200, float('inf')]
    price_labels = ['0-10', '10-25', '25-50', '50-100', '100-200', '200+']

    train_data['price_range'] = pd.cut(train_data['price'], bins=price_ranges, labels=price_labels, include_lowest=True)
    val_data['price_range'] = pd.cut(val_data['price'], bins=price_ranges, labels=price_labels, include_lowest=True)
    test_data['price_range'] = pd.cut(test_data['price'], bins=price_ranges, labels=price_labels, include_lowest=True)

    # Apply mappings to all splits
    train_data = apply_amazon_mappings(train_data, user_mapping, item_mapping, brand_mapping, category_mapping, price_range_mapping)
    val_data = apply_amazon_mappings(val_data, user_mapping, item_mapping, brand_mapping, category_mapping, price_range_mapping)
    test_data = apply_amazon_mappings(test_data, user_mapping, item_mapping, brand_mapping, category_mapping, price_range_mapping)

    # Save processed datasets
    train_data.to_csv(os.path.join(save_path, 'train.csv'), index=False)
    val_data.to_csv(os.path.join(save_path, 'val.csv'), index=False)
    test_data.to_csv(os.path.join(save_path, 'test.csv'), index=False)

    print("Saved processed datasets:")
    print(f"  Train: {os.path.join(save_path, 'train.csv')}")
    print(f"  Val: {os.path.join(save_path, 'val.csv')}")
    print(f"  Test: {os.path.join(save_path, 'test.csv')}")

    return train_data, val_data, test_data

def copy_other_mappings_to_processed_dir(save_path):
    """Copy other mapping files to processed directory"""
    print("All mappings already saved to processed directory!")

if __name__ == "__main__":
    # Define paths
    data_path = '/data/datasets/amazon/amazon_process.csv'
    save_path = '/data/datasets/processed_datasets/amazon'

    # Load the Amazon dataset
    df = load_amazon_data(data_path)

    # Display basic information
    print(f"\nAmazon Dataset Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Date range: {df['unixReviewTime_convert'].min()} to {df['unixReviewTime_convert'].max()}")
    print(f"Unique users: {df['reviewerID'].nunique()}")
    print(f"Unique items: {df['asin'].nunique()}")
    print(f"Label distribution:")
    print(df['label'].value_counts())

    # Preprocess features
    df = preprocess_amazon_features(df)

    # Filter cold-start data
    print("\nFiltering cold-start data...")
    df = filter_cold_start_amazon(df, min_user_interactions=5, min_item_interactions=5)

    # Split the dataset
    print("\nSplitting dataset into train/val/test sets...")
    train_data, val_data, test_data = split_amazon_dataset(df)

    # Create final mappings based on actual train/val/test data
    train_data, val_data, test_data = create_final_amazon_mappings(train_data, val_data, test_data, save_path)

    # Copy other mapping files to processed directory
    print("\nCopying other mapping files to processed directory...")
    copy_other_mappings_to_processed_dir(save_path)

    print("\n✅ Amazon dataset preprocessing completed successfully!")
    print(f"📁 Processed data saved to: {save_path}")
    print(f"📊 Final statistics:")
    print(f"   Train: {len(train_data)} samples")
    print(f"   Val: {len(val_data)} samples")
    print(f"   Test: {len(test_data)} samples")
