#!/usr/bin/env python3
"""
Train DCNv2 model on Amazon dataset for comparative study
"""

import sys
import os
import pandas as pd
import numpy as np
import torch
import json
from datetime import datetime

# Add DCNv2 to path
sys.path.append('GraphLLM4CTR_baseline/DCNv2')

from GraphLLM4CTR_baseline.DCNv2.train import train_dcnv2_model
from GraphLLM4CTR_baseline.DCNv2.config import DCNv2Config
from GraphLLM4CTR_baseline.DCNv2.utils.data_processor import DataProcessor

def prepare_amazon_data():
    """Prepare Amazon data for DCNv2 training"""
    print("📊 Preparing Amazon data for DCNv2...")
    
    # Load the Amazon data with split categories
    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')
    
    print(f"Train shape: {train_df.shape}")
    print(f"Val shape: {val_df.shape}")
    print(f"Test shape: {test_df.shape}")
    
    # DCNv2 expects specific column format
    # Map Amazon columns to DCNv2 expected format
    feature_columns = ['user_id', 'item_id', 'brand_index', 'price_range_index', 
                      'category_1', 'category_2', 'category_3', 'price']
    
    # Ensure all feature columns are present
    for col in feature_columns:
        if col not in train_df.columns:
            print(f"❌ Missing column: {col}")
            return None
    
    # Save processed data for DCNv2
    output_dir = 'GraphLLM4CTR_baseline/DCNv2/data/amazon'
    os.makedirs(output_dir, exist_ok=True)
    
    train_df[feature_columns + ['label']].to_csv(f'{output_dir}/train.csv', index=False)
    val_df[feature_columns + ['label']].to_csv(f'{output_dir}/val.csv', index=False)
    test_df[feature_columns + ['label']].to_csv(f'{output_dir}/test.csv', index=False)
    
    print(f"✅ Data saved to {output_dir}")
    return output_dir

def train_amazon_dcnv2():
    """Train DCNv2 on Amazon dataset"""
    print("🚀 Starting DCNv2 training on Amazon dataset...")
    
    # Prepare data
    data_dir = prepare_amazon_data()
    if data_dir is None:
        print("❌ Data preparation failed!")
        return
    
    # Configure DCNv2 for Amazon dataset
    config = DCNv2Config()
    config.dataset_name = "Amazon"
    config.data_dir = data_dir
    config.output_dir = "GraphLLM4CTR_baseline/DCNv2/output_amazon_dcnv2"
    config.epochs = 10
    config.batch_size = 1024
    config.learning_rate = 0.001
    config.embedding_dim = 16
    config.hidden_dims = [256, 128]
    config.dropout = 0.2
    config.seed = 2023
    
    # Train the model
    try:
        results = train_dcnv2_model(config)
        
        # Save results
        os.makedirs(config.output_dir, exist_ok=True)
        with open(f'{config.output_dir}/results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print("✅ DCNv2 training completed!")
        print(f"📊 Results: AUC={results.get('test_auc', 'N/A'):.4f}, "
              f"Logloss={results.get('test_logloss', 'N/A'):.4f}")
        
        return results
        
    except Exception as e:
        print(f"❌ DCNv2 training failed: {e}")
        return None

if __name__ == "__main__":
    train_amazon_dcnv2()
